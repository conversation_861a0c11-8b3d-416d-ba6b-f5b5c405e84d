<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\SaleItem;
use Illuminate\Support\Facades\DB;

class SaleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $sales = Sale::with(['customer', 'saleItems.product'])->latest()->get();
        return view('sales.index', compact('sales'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::where('stock', '>', 0)->get();
        $customers = Customer::all();
        return view('sales.create', compact('products', 'customers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        DB::transaction(function () use ($request) {
            // ایجاد یا پیدا کردن مشتری
            $customer = null;
            if ($request->customer_name) {
                $customer = Customer::firstOrCreate([
                    'name' => $request->customer_name,
                    'phone' => $request->customer_phone,
                ]);
            }

            // محاسبه مجموع قیمت
            $totalAmount = 0;
            $saleItems = [];

            foreach ($request->products as $productData) {
                $product = Product::find($productData['id']);

                // بررسی موجودی
                if ($product->stock < $productData['quantity']) {
                    throw new \Exception("موجودی محصول {$product->name} کافی نیست.");
                }

                $unitPrice = $product->price;
                $totalPrice = $unitPrice * $productData['quantity'];
                $totalAmount += $totalPrice;

                $saleItems[] = [
                    'product_id' => $product->id,
                    'quantity' => $productData['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                ];

                // کاهش موجودی
                $product->decrement('stock', $productData['quantity']);
            }

            // ایجاد فروش
            $sale = Sale::create([
                'customer_id' => $customer ? $customer->id : null,
                'total_amount' => $totalAmount,
            ]);

            // ایجاد آیتم‌های فروش
            foreach ($saleItems as $item) {
                $sale->saleItems()->create($item);
            }
        });

        return redirect()->route('sales.index')->with('success', 'فروش با موفقیت ثبت شد.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product']);
        return view('sales.show', compact('sale'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // فروش‌ها معمولاً قابل ویرایش نیستند
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // فروش‌ها معمولاً قابل ویرایش نیستند
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sale $sale)
    {
        // بازگرداندن موجودی محصولات
        foreach ($sale->saleItems as $item) {
            $item->product->increment('stock', $item->quantity);
        }

        $sale->delete();
        return redirect()->route('sales.index')->with('success', 'فروش با موفقیت حذف شد.');
    }
}
