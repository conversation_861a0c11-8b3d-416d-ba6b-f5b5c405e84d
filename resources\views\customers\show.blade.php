@extends('layouts.app')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات مشتری: {{ $customer->name }}</h1>
            <div>
                <a href="{{ route('customers.edit', $customer) }}" class="btn btn-warning">ویرایش</a>
                <a href="{{ route('customers.index') }}" class="btn btn-secondary">بازگشت به لیست</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5>اطلاعات مشتری</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>شناسه:</strong></td>
                        <td>{{ $customer->id }}</td>
                    </tr>
                    <tr>
                        <td><strong>نام:</strong></td>
                        <td>{{ $customer->name }}</td>
                    </tr>
                    <tr>
                        <td><strong>شماره تماس:</strong></td>
                        <td>{{ $customer->phone ?: 'ثبت نشده' }}</td>
                    </tr>
                    <tr>
                        <td><strong>تعداد خریدها:</strong></td>
                        <td>{{ $customer->sales->count() }} خرید</td>
                    </tr>
                    <tr>
                        <td><strong>مجموع خریدها:</strong></td>
                        <td>{{ number_format($customer->sales->sum('total_amount')) }} تومان</td>
                    </tr>
                    <tr>
                        <td><strong>تاریخ ثبت:</strong></td>
                        <td>{{ $customer->created_at->format('Y/m/d H:i') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>عملیات</h5>
            </div>
            <div class="card-body text-center">
                <a href="{{ route('customers.edit', $customer) }}" class="btn btn-warning btn-block mb-2">ویرایش مشتری</a>
                <a href="{{ route('sales.create') }}" class="btn btn-success btn-block mb-2">ثبت فروش جدید</a>
                <form action="{{ route('customers.destroy', $customer) }}" method="POST" class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger btn-block">حذف مشتری</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>تاریخچه خریدها</h5>
            </div>
            <div class="card-body">
                @if($customer->sales->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>شماره فروش</th>
                                    <th>تعداد اقلام</th>
                                    <th>مبلغ</th>
                                    <th>تاریخ</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customer->sales as $sale)
                                <tr>
                                    <td>#{{ $sale->id }}</td>
                                    <td>{{ $sale->saleItems->count() }} قلم</td>
                                    <td>{{ number_format($sale->total_amount) }} تومان</td>
                                    <td>{{ $sale->created_at->format('Y/m/d H:i') }}</td>
                                    <td>
                                        <a href="{{ route('sales.show', $sale) }}" class="btn btn-sm btn-info">جزئیات</a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-muted">این مشتری هنوز خریدی نداشته است.</p>
                        <a href="{{ route('sales.create') }}" class="btn btn-primary">ثبت اولین فروش</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
